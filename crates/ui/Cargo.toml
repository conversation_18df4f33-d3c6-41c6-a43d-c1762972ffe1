[package]
name = "ui"
version = "0.1.0"
edition = "2024"

[dependencies]
anyhow = "1.0.98"
enum-iterator = "2.1.0"
gpui = { git = "https://github.com/zed-industries/zed.git" }
itertools = "0.14.0"
once_cell = "1.21.3"
paste = "1.0.15"
regex = "1.11.1"
rust-embed = "8.7.2"
schemars = "0.8.22"
serde = "1.0.219"
serde_json = "1.0.140"
smallvec = "1.15.0"
tracing = "0.1.41"
tree-sitter = "0.25.6"
tree-sitter-bash = "0.25.0"
tree-sitter-c = "0.24.1"
tree-sitter-c-sharp = "0.23.1"
tree-sitter-cmake = "0.7.1"
tree-sitter-cpp = "0.23.4"
tree-sitter-css = "0.23.2"
tree-sitter-diff = "0.1.0"
tree-sitter-elixir = "0.3.4"
tree-sitter-embedded-template = "0.23.2"
tree-sitter-go = "0.23.4"
tree-sitter-graphql = "0.1.0"
tree-sitter-highlight = "0.25.6"
tree-sitter-html = "0.23.2"
tree-sitter-java = "0.23.5"
tree-sitter-javascript = "0.23.1"
tree-sitter-json = "0.24.8"
tree-sitter-make = "1.1.1"
tree-sitter-md = "0.3.2"
tree-sitter-proto = "0.2.0"
tree-sitter-python = "0.23.6"
tree-sitter-ruby = "0.23.1"
tree-sitter-rust = "0.24.0"
tree-sitter-scala = "0.23.4"
tree-sitter-sequel = "0.3.8"
tree-sitter-swift = "0.7.0"
tree-sitter-toml-ng = "0.7.0"
tree-sitter-typescript = "0.23.2"
tree-sitter-yaml = "0.7.1"
tree-sitter-zig = "1.1.2"
unicode-segmentation = "1.12.0"
uuid = "1.17.0"

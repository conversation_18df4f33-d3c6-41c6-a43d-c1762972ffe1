use gpui::{App, ClickEvent, FocusableWrapper, InteractiveElement, Stateful, Window};

pub trait InteractiveElementExt: InteractiveElement {
    /// Set the listener for a double click event.
    fn on_double_click(
        mut self,
        listener: impl Fn(&ClickEvent, &mut Window, &mut App) + 'static,
    ) -> Self
    where
        Self: Sized,
    {
        self.interactivity().on_click(move |event, window, cx| {
            if event.up.click_count == 2 {
                listener(event, window, cx);
            }
        });
        self
    }
}

impl<E: InteractiveElement> InteractiveElementExt for FocusableWrapper<E> {}
impl<E: InteractiveElement> InteractiveElementExt for Stateful<E> {}